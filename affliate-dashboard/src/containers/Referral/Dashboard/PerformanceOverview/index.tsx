import React, { useState, useEffect } from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { useSelector, useDispatch } from "react-redux";
import { trackLinksActions } from "@/features/rootActions";
import { RootState } from "@/store";
import { cn } from "@/lib/utils";
import { useIsMobile, touchButton, responsiveText, mobileSpacing } from "@/utils/mobile-responsive";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

type TimePeriod = "weekly" | "monthly" | "yearly";

const PerformanceOverview: React.FC = () => {
  const [timePeriod, setTimePeriod] = useState<TimePeriod>("weekly");
  const [isCollapsed, setIsCollapsed] = useState(false);
  const dispatch = useDispatch();
  const isMobile = useIsMobile();

  const performanceOverview = useSelector(
    (state: RootState) => state.trackLinks.performanceOverview
  );
  const performanceLoading = useSelector(
    (state: RootState) => state.trackLinks.performanceLoading
  );

  useEffect(() => {
    // Map time period to API format
    const periodMap = {
      weekly: "weekly",
      monthly: "monthly",
      yearly: "yearly",
    };

    dispatch(
      trackLinksActions.fetchPerformanceOverviewRequest({
        period: periodMap[timePeriod],
      })
    );
  }, [dispatch, timePeriod]);

  // Generate chart data from performance overview or use fallback
  const chartData = {
    labels: performanceOverview?.time || [
      "Apr 30",
      "May 07",
      "May 14",
      "May 21",
    ],
    datasets: [
      {
        label: "Visitors",
        data: performanceOverview?.visitors || [42, 55, 39, 65],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.5)",
        tension: 0.4,
      },
      {
        label: "Leads",
        data: performanceOverview?.leads || [15, 18, 14, 22],
        borderColor: "rgb(245, 158, 11)",
        backgroundColor: "rgba(245, 158, 11, 0.5)",
        tension: 0.4,
      },
      {
        label: "Conversions",
        data: performanceOverview?.conversions || [8, 12, 7, 15],
        borderColor: "rgb(16, 185, 129)",
        backgroundColor: "rgba(16, 185, 129, 0.5)",
        tension: 0.4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
    plugins: {
      legend: {
        position: "top" as const,
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        padding: 12,
        titleFont: {
          size: isMobile ? 12 : 14,
        },
        bodyFont: {
          size: isMobile ? 11 : 13,
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          display: true,
          color: 'rgba(156, 163, 175, 0.2)',
        },
        border: {
          display: false,
        },
        ticks: {
          font: {
            size: isMobile ? 10 : 12,
          },
          color: 'rgba(156, 163, 175, 0.8)',
        },
      },
      x: {
        grid: {
          display: false,
        },
        border: {
          display: false,
        },
        ticks: {
          font: {
            size: isMobile ? 10 : 12,
          },
          color: 'rgba(156, 163, 175, 0.8)',
          maxRotation: isMobile ? 45 : 0,
        },
      },
    },
    elements: {
      point: {
        radius: isMobile ? 3 : 4,
        hoverRadius: isMobile ? 5 : 6,
      },
      line: {
        borderWidth: isMobile ? 2 : 3,
      },
    },
  };

  return (
    <div className={cn(
      "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700",
      mobileSpacing('md'),
      "py-4 sm:py-6"
    )}>
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 space-y-3 sm:space-y-0">
        <div className="flex items-center justify-between">
          <h2 className={cn(
            "font-medium text-gray-900 dark:text-white",
            responsiveText('lg')
          )}>
            Performance Overview
          </h2>
          {isMobile && (
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className={touchButton('icon', 'text-gray-500 dark:text-gray-400')}
              aria-label={isCollapsed ? 'Expand chart' : 'Collapse chart'}
            >
              <svg
                className={cn("w-5 h-5 transition-transform", isCollapsed && "rotate-180")}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          )}
        </div>
        <div className={cn(
          "flex space-x-1 sm:space-x-2",
          isMobile && "overflow-x-auto scrollbar-thin"
        )}>
          {(['weekly', 'monthly', 'yearly'] as TimePeriod[]).map((period) => (
            <button
              key={period}
              onClick={() => setTimePeriod(period)}
              className={cn(
                touchButton('small'),
                "transition-colors whitespace-nowrap",
                timePeriod === period
                  ? "bg-blue-600 text-white"
                  : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600",
                performanceLoading && "opacity-50 cursor-not-allowed"
              )}
              disabled={performanceLoading}
            >
              {period.charAt(0).toUpperCase() + period.slice(1)}
            </button>
          ))}
        </div>
      </div>
      {(!isMobile || !isCollapsed) && (
        <div className={cn(
          "relative",
          isMobile ? "h-[250px]" : "h-[200px]"
        )}>
          {performanceLoading && (
            <div className="absolute inset-0 bg-gray-50 dark:bg-gray-700 bg-opacity-50 flex items-center justify-center z-10 rounded-lg">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          )}
          <Line options={chartOptions} data={chartData} />
        </div>
      )}

      {(!isMobile || !isCollapsed) && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-4 gap-3">
          <p className={cn(
            "text-gray-500 dark:text-gray-400",
            responsiveText('xs')
          )}>
            Performance data from your links
          </p>

          {/* Color Legend */}
          <div className={cn(
            "flex flex-wrap items-center gap-3",
            responsiveText('xs')
          )}>
            <div className="flex items-center gap-1.5">
              <div className={cn(
                "rounded-full bg-blue-500",
                isMobile ? "w-3 h-3" : "w-2.5 h-2.5"
              )}></div>
              <span className="text-gray-500 dark:text-gray-400">Visitors</span>
            </div>
            <div className="flex items-center gap-1.5">
              <div className={cn(
                "rounded-full bg-amber-500",
                isMobile ? "w-3 h-3" : "w-2.5 h-2.5"
              )}></div>
              <span className="text-gray-500 dark:text-gray-400">Leads</span>
            </div>
            <div className="flex items-center gap-1.5">
              <div className={cn(
                "rounded-full bg-emerald-500",
                isMobile ? "w-3 h-3" : "w-2.5 h-2.5"
              )}></div>
              <span className="text-gray-500 dark:text-gray-400">
                Conversions
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PerformanceOverview;
