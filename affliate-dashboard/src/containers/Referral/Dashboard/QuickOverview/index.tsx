import React, { useEffect } from "react";
import {
  Arrow<PERSON><PERSON>,
  Arrow<PERSON><PERSON>,
  TrendingDown,
  Minus,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { useSelector, useDispatch } from "react-redux";
import { trackLinksActions } from "@/features/rootActions";
import { RootState } from "@/store";
import { cn } from "@/lib/utils";
import { useIsMobile, responsiveText, mobileSpacing } from "@/utils/mobile-responsive";

interface QuickOverviewCardProps {
  title: string;
  value: number;
  description: string;
  trend?: "up" | "down" | "stable";
  trendColor?: string;
  isLoading?: boolean;
}

const QuickOverviewCard: React.FC<QuickOverviewCardProps> = ({
  title,
  value,
  description,
  trend = "stable",
  trendColor,
  isLoading = false,
}) => {
  const isMobile = useIsMobile();

  const getTrendIcon = () => {
    const iconSize = isMobile ? "w-5 h-5" : "w-4 h-4";
    switch (trend) {
      case "up":
        return <ArrowUp className={iconSize} />;
      case "down":
        return <TrendingDown className={iconSize} />;
      default:
        return <Minus className={iconSize} />;
    }
  };

  const defaultTrendColor = () => {
    switch (trend) {
      case "up":
        return "text-green-500";
      case "down":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  if (isLoading) {
    return (
      <div className={cn(
        "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700",
        mobileSpacing('md'),
        "py-4 sm:py-6"
      )}>
        <div className="animate-pulse">
          <div className="flex justify-between items-start">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
            <div className={cn(
              "bg-gray-200 dark:bg-gray-700 rounded",
              isMobile ? "h-5 w-5" : "h-4 w-4"
            )}></div>
          </div>
          <div className="mt-3">
            <div className={cn(
              "bg-gray-200 dark:bg-gray-700 rounded",
              isMobile ? "h-10 w-20" : "h-8 w-16"
            )}></div>
          </div>
          <div className={cn(
            "bg-gray-200 dark:bg-gray-700 rounded w-32 mt-2",
            isMobile ? "h-4" : "h-3"
          )}></div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 transition-all hover:shadow-md",
      mobileSpacing('md'),
      "py-4 sm:py-6"
    )}>
      <div className="flex justify-between items-start">
        <h3 className={cn(
          "font-medium text-gray-500 dark:text-gray-400",
          responsiveText('sm')
        )}>
          {title}
        </h3>
        <div className={cn(
          trendColor || defaultTrendColor(),
          "flex-shrink-0"
        )}>
          {getTrendIcon()}
        </div>
      </div>
      <div className="mt-3">
        <span className={cn(
          "font-bold text-gray-900 dark:text-white",
          isMobile ? "text-3xl" : "text-2xl"
        )}>
          {value.toLocaleString()}
        </span>
      </div>
      <p className={cn(
        "text-gray-500 dark:text-gray-400 mt-2",
        responsiveText('xs')
      )}>
        {description}
      </p>
    </div>
  );
};

const QuickOverview: React.FC = () => {
  const dispatch = useDispatch();
  const overview = useSelector((state: RootState) => state.trackLinks.overview);
  const isLoading = useSelector((state: RootState) => state.trackLinks.loading);
  const error = useSelector((state: RootState) => state.trackLinks.error);

  useEffect(() => {
    dispatch(trackLinksActions.fetchOverviewRequest());
  }, [dispatch]);

  if (error) {
    return (
      <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
        <div className="flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
          <p className="text-red-700 dark:text-red-400">
            Failed to load overview: {error}
          </p>
        </div>
      </div>
    );
  }

  const mapTrend = (
    trend: "increase" | "decrease" | "stable"
  ): "up" | "down" | "stable" => {
    switch (trend) {
      case "increase":
        return "up";
      case "decrease":
        return "down";
      default:
        return "stable";
    }
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
      <QuickOverviewCard
        title="Total Visitors"
        value={overview?.visitors.total || 0}
        description="All visitors from your links"
        trend={overview ? mapTrend(overview.visitors.trend) : "stable"}
        isLoading={isLoading}
      />
      <QuickOverviewCard
        title="Total Leads"
        value={overview?.leads.total || 0}
        description="All leads from your links"
        trend={overview ? mapTrend(overview.leads.trend) : "stable"}
        isLoading={isLoading}
      />
      <div className="sm:col-span-2 lg:col-span-1">
        <QuickOverviewCard
          title="Total Conversions"
          value={overview?.conversions.total || 0}
          description="All conversions from your links"
          trend={overview ? mapTrend(overview.conversions.trend) : "stable"}
          trendColor="text-green-500"
          isLoading={isLoading}
        />
      </div>
    </div>
  );
};

export default QuickOverview;
