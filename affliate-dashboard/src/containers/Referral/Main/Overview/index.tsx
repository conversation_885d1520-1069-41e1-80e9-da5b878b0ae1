import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DollarSign,
  Star,
  TrendingUp,
  ArrowRight,
  Sparkles,
} from "lucide-react";
import { Loader2, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { useIsMobile, touchButton, responsiveText, mobileSpacing } from "@/utils/mobile-responsive";

interface OverviewProps {
  onJoinClick: () => void;
  isLoading?: boolean;
  error?: string | null;
}

const Overview: React.FC<OverviewProps> = ({
  onJoinClick,
  isLoading = false,
  error = null,
}) => {
  const isMobile = useIsMobile();

  return (
    <section className={cn(
      "text-center relative overflow-hidden bg-gradient-to-b from-background to-background/60",
      mobileSpacing('md'),
      "py-8 sm:py-12"
    )}>
      {/* Decorative elements - adjusted for mobile */}
      <div className={cn(
        "absolute bg-primary/10 rounded-full blur-3xl",
        isMobile ? "top-10 left-5 w-16 h-16" : "top-20 left-10 w-20 h-20"
      )}></div>
      <div className={cn(
        "absolute bg-primary/10 rounded-full blur-3xl",
        isMobile ? "bottom-10 right-5 w-20 h-20" : "bottom-20 right-10 w-32 h-32"
      )}></div>

      <div className="relative z-10">
        {/* Header */}
        <div className="mb-4 sm:mb-6">
          <span className={cn(
            "inline-flex items-center gap-2 font-medium text-primary mb-3",
            responsiveText('sm')
          )}>
            <Sparkles className={isMobile ? "h-5 w-5" : "h-4 w-4"} />
            <span>EARN WITH US</span>
          </span>
        </div>

        <h1 className={cn(
          "font-bold mb-6 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground inline-block rounded-2xl shadow-lg",
          isMobile ? "text-3xl px-6 py-2" : "text-4xl md:text-6xl px-8 py-3"
        )}>
          Affiliate Program
        </h1>

        <div className={cn(
          "max-w-2xl mx-auto mb-8 sm:mb-12 text-foreground/90 leading-relaxed",
          responsiveText('lg')
        )}>
          <p>
            Join our affiliate program and earn commissions by referring new
            customers to our platform. It's easy to get started, and you can
            start earning right away!
          </p>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-10 sm:mb-14 max-w-5xl mx-auto">
          {[
            {
              icon: DollarSign,
              title: "Earn Money",
              description:
                "Get up to 50% commission on every successful referral",
              gradient: "from-green-500 to-emerald-600",
            },
            {
              icon: Star,
              title: "Premium Support",
              description: "Access to exclusive tools and affiliate support",
              gradient: "from-purple-500 to-indigo-600",
            },
            {
              icon: TrendingUp,
              title: "Growth Opportunities",
              description: "Scale your earnings with performance-based bonuses",
              gradient: "from-amber-500 to-orange-600",
            },
          ].map((feature, index) => (
            <div
              key={index}
              className={cn(
                "bg-card/80 backdrop-blur-sm border border-border rounded-2xl shadow-sm flex flex-col items-center group transition-all duration-300",
                isMobile ? "p-6 hover:shadow-lg" : "p-8 hover:shadow-xl hover:-translate-y-1 hover:scale-[1.03]",
                index === 2 && "sm:col-span-2 lg:col-span-1" // Make last card span 2 columns on tablet
              )}
            >
              <div
                className={cn(
                  `bg-gradient-to-br ${feature.gradient} rounded-2xl flex items-center justify-center mb-4 sm:mb-5 shadow-md group-hover:shadow-lg transition-all duration-300`,
                  isMobile ? "w-12 h-12" : "w-16 h-16"
                )}
              >
                <feature.icon
                  className={cn("text-white", isMobile ? "w-6 h-6" : "w-8 h-8")}
                  strokeWidth={2}
                />
              </div>
              <h3 className={cn(
                "font-bold mb-3 text-foreground text-center",
                responsiveText('lg')
              )}>
                {feature.title}
              </h3>
              <p className={cn(
                "text-center text-foreground/80 mb-4",
                responsiveText('sm')
              )}>
                {feature.description}
              </p>
              {!isMobile && (
                <div className="mt-auto text-primary flex items-center text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                  <span>Learn more</span>
                  <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-10 bg-card/30 backdrop-blur-sm py-10 px-6 rounded-3xl max-w-3xl mx-auto border border-border/50">
          <h3 className="text-2xl font-bold mb-4">
            Ready to boost your income?
          </h3>
          <p className="mb-6 text-foreground/80 max-w-lg mx-auto">
            Start earning passive income by sharing our platform with your
            audience. No upfront costs, just rewards.
          </p>
          <div className="transition-transform duration-200 hover:scale-[1.03] active:scale-[0.98]">
            {/* <Button
              size="lg"
              className="px-12 py-7 text-lg font-medium rounded-full shadow-md hover:shadow-xl transition-all bg-gradient-to-r from-primary to-primary/80 relative overflow-hidden group"
              onClick={onJoinClick}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                "Get Started"
              )}
            </Button> */}
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="w-full max-w-md p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center">
            <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400 mr-2 flex-shrink-0" />
            <span className="text-sm text-red-700 dark:text-red-300">
              {error}
            </span>
          </div>
        )}
      </div>
    </section>
  );
};

export default Overview;
