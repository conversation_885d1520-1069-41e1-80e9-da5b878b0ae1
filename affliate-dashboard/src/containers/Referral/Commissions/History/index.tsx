import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectReferralCommissions,
  selectReferralCommissionsLoading,
  selectReferralCommissionsPagination,
} from "@/features/selectors";
import { referralCommissionActions } from "@/features/rootActions";
import { ReferralCommission } from "@/features/referral-commission/referral-commission.slice";
import TableCommissions, {
  CommissionItem,
} from "@/components/TableCommissions";
import MobileTable, { MobileTableColumn, MobileTableBadge } from "@/components/ui/mobile-table";
import MobilePagination from "@/components/ui/mobile-pagination";
import { cn } from "@/lib/utils";
import { useIsMobile, responsiveText, mobileSpacing } from "@/utils/mobile-responsive";

interface HistoryProps {
  loading?: boolean;
}

const History: React.FC<HistoryProps> = ({
  loading: externalLoading = false,
}) => {
  const dispatch = useDispatch();
  const isMobile = useIsMobile();

  // Redux selectors
  const commissions = useSelector(selectReferralCommissions);
  const isLoading = useSelector(selectReferralCommissionsLoading);
  const pagination = useSelector(selectReferralCommissionsPagination);

  const [currentPage, setCurrentPage] = useState(1);

  // Transform backend data to match TableCommissions interface
  const transformCommissionData = (
    commissions: ReferralCommission[]
  ): CommissionItem[] => {
    return commissions.map((commission) => ({
      id: commission.id.toString(),
      date: new Date(commission.createdAt).toLocaleDateString("en-US", {
        month: "numeric",
        day: "numeric",
        year: "numeric",
      }),
      user: {
        username: commission.referral.user?.username || "Unknown",
        email: commission.referral.user?.email || "Unknown",
      },
      product: commission.subscription_tier?.display_name || "Unknown",
      productType: commission.subscription_tier?.display_name || "Unknown",
      grossSale: commission.gross_sale_amount,
      commission: commission.commission_amount,
      status: (() => {
        // Map commission status to table status
        if (commission.commission_status === "pending") return "Under Review";
        if (commission.commission_status === "paid") return "Paid";
        if (commission.commission_status === "ready_to_pay")
          return "Ready To Pay";
        return commission.commission_status as
          | "Paid"
          | "Ready To Pay"
          | "Under Review";
      })(),
    }));
  };

  // Only fetch data when page changes (not on mount)
  useEffect(() => {
    // Only fetch if we're not on the first page or if we don't have data yet
    if (currentPage > 1) {
      dispatch(
        referralCommissionActions.fetchCommissionsRequest({
          page: currentPage,
          pageSize: 25,
        })
      );
    }
  }, [dispatch, currentPage]);

  const goToPage = (page: number) => {
    setCurrentPage(page);
  };

  // Transform commission data for table
  const commissionHistory = transformCommissionData(commissions);

  // Use the loading state from Redux or external prop
  const tableLoading = isLoading || externalLoading;

  // Mobile table columns configuration
  const mobileTableColumns: MobileTableColumn<CommissionItem>[] = [
    {
      key: 'date',
      header: 'Date',
      accessor: 'date',
      mobileLabel: 'Date',
    },
    {
      key: 'customer',
      header: 'Customer',
      accessor: (item) => item.user.username,
      mobileLabel: 'Customer',
    },
    {
      key: 'product',
      header: 'Product',
      accessor: 'product',
      mobileLabel: 'Product',
      hideOnMobile: true, // Hide on mobile to save space
    },
    {
      key: 'grossSale',
      header: 'Gross Sale',
      accessor: (item) => `$${item.grossSale.toFixed(2)}`,
      mobileLabel: 'Sale',
    },
    {
      key: 'commission',
      header: 'Commission',
      accessor: (item) => `$${item.commission.toFixed(2)}`,
      mobileLabel: 'Commission',
    },
    {
      key: 'status',
      header: 'Status',
      accessor: (item) => item.status,
      mobileLabel: 'Status',
    },
  ];

  // Mobile card renderer for commissions
  const renderMobileCommissionCard = (item: CommissionItem, index: number) => (
    <div className="space-y-3">
      {/* Header with customer and date */}
      <div className="flex justify-between items-start">
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-gray-900 dark:text-white truncate">
            {item.user.username}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {item.date}
          </p>
        </div>
        <MobileTableBadge
          variant={
            item.status === 'Paid' ? 'success' :
            item.status === 'Ready To Pay' ? 'success' :
            item.status === 'Under Review' ? 'warning' : 'default'
          }
        >
          {item.status}
        </MobileTableBadge>
      </div>

      {/* Product info */}
      <div className="text-sm text-gray-600 dark:text-gray-300">
        <span className="font-medium">{item.product}</span>
      </div>

      {/* Financial info */}
      <div className="grid grid-cols-2 gap-4 text-center bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
        <div>
          <div className="text-lg font-semibold text-gray-900 dark:text-white">
            ${item.grossSale.toFixed(2)}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">Gross Sale</div>
        </div>
        <div>
          <div className="text-lg font-semibold text-green-600 dark:text-green-400">
            ${item.commission.toFixed(2)}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">Commission</div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
      <div className={cn(
        "border-b border-gray-200 dark:border-gray-700",
        mobileSpacing('md'),
        "py-4"
      )}>
        <h3 className={cn(
          "font-medium text-gray-900 dark:text-white",
          responsiveText('lg')
        )}>
          Commission History
        </h3>
      </div>

      {/* Use mobile-responsive table */}
      {isMobile ? (
        <div className={mobileSpacing('md')}>
          <MobileTable
            data={commissionHistory}
            columns={mobileTableColumns}
            isLoading={tableLoading}
            emptyMessage="No commission history yet"
            renderMobileCard={renderMobileCommissionCard}
            showMobileCards={true}
            keyExtractor={(item) => item.id}
          />

          {/* Mobile pagination */}
          {pagination && pagination.pageCount > 1 && (
            <div className="mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
              <MobilePagination
                currentPage={currentPage}
                totalPages={pagination.pageCount}
                onPageChange={goToPage}
                isLoading={isLoading}
                showPageInfo={true}
                pageSize={pagination.pageSize}
                totalItems={pagination.total}
              />
            </div>
          )}
        </div>
      ) : (
        <TableCommissions
          data={commissionHistory}
          isLoading={tableLoading}
          enablePagination={true}
          pagination={
            pagination || {
              page: currentPage,
              pageSize: 10,
              pageCount: 1,
              total: commissionHistory.length,
            }
          }
          currentPage={currentPage}
          onPageChange={goToPage}
          isPaginationLoading={isLoading && currentPage !== 1}
        />
      )}
    </div>
  );
};

export default History;
