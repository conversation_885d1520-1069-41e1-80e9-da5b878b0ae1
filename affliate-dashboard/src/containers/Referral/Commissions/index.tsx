import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectReferralCommissions,
  selectReferralCommissionsLoading,
  selectReferralCommissionsError,
  selectReferralCommissionStats,
  selectReferralCommissionStatsLoading,
} from "@/features/selectors";
import { referralCommissionActions } from "@/features/rootActions";
import Overview from "./Overview";
import History from "./History";
import { cn } from "@/lib/utils";
import { useIsMobile, responsiveText, mobileSpacing } from "@/utils/mobile-responsive";

const CommissionsContainer: React.FC = () => {
  const dispatch = useDispatch();

  // Redux selectors
  const commissions = useSelector(selectReferralCommissions);
  const isLoading = useSelector(selectReferralCommissionsLoading);
  const error = useSelector(selectReferralCommissionsError);
  const stats = useSelector(selectReferralCommissionStats);
  const statsLoading = useSelector(selectReferralCommissionStatsLoading);

  // Fetch data on mount
  useEffect(() => {
    // Fetch both stats and commission history
    dispatch(referralCommissionActions.fetchStatsRequest());
    dispatch(
      referralCommissionActions.fetchCommissionsRequest({
        page: 1,
        pageSize: 25,
      })
    );
  }, [dispatch]);

  const isMobile = useIsMobile();

  return (
    <div className={cn(
      "space-y-6 max-w-full overflow-x-hidden",
      mobileSpacing('md'),
      "py-6"
    )}>
      <div>
        <h1 className={cn(
          "font-semibold text-gray-800 dark:text-white",
          responsiveText('xl')
        )}>
          Commissions
        </h1>
        <p className={cn(
          "text-gray-600 dark:text-gray-300 mt-2",
          responsiveText('sm')
        )}>
          Track your commission earnings from leads and conversions.
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className={cn(
          "bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",
          mobileSpacing('sm'),
          "py-3 sm:py-4"
        )}>
          <p className={cn(
            "text-red-700 dark:text-red-300",
            responsiveText('sm')
          )}>
            {error}
          </p>
        </div>
      )}

      {/* Overview Cards */}
      <div className="space-y-1">
        <Overview loading={statsLoading} stats={stats} />
      </div>

      {/* Commissions History Table */}
      <div className="space-y-1">
        <History loading={isLoading} />
      </div>
    </div>
  );
};

export default CommissionsContainer;
