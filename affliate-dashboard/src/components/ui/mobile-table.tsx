/**
 * Mobile-Responsive Table Component
 * 
 * A flexible table component that adapts to different screen sizes:
 * - Desktop: Traditional table layout
 * - Tablet: Horizontal scrolling
 * - Mobile: Stacked card layout
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { useIsMobile, useIsTablet, touchButton } from '@/utils/mobile-responsive';
import { Loader2 } from 'lucide-react';

export interface MobileTableColumn<T = any> {
  key: string;
  header: string;
  accessor: keyof T | ((item: T) => React.ReactNode);
  className?: string;
  headerClassName?: string;
  sortable?: boolean;
  width?: string;
  mobileLabel?: string; // Custom label for mobile view
  hideOnMobile?: boolean; // Hide column on mobile
}

export interface MobileTableProps<T = any> {
  data: T[];
  columns: MobileTableColumn<T>[];
  isLoading?: boolean;
  emptyMessage?: string;
  className?: string;
  onRowClick?: (item: T, index: number) => void;
  renderMobileCard?: (item: T, index: number) => React.ReactNode;
  showMobileCards?: boolean; // Force mobile card view
  keyExtractor?: (item: T, index: number) => string;
}

const MobileTable = <T extends Record<string, any>>({
  data,
  columns,
  isLoading = false,
  emptyMessage = 'No data available',
  className,
  onRowClick,
  renderMobileCard,
  showMobileCards = false,
  keyExtractor = (_, index) => index.toString(),
}: MobileTableProps<T>) => {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  
  // Determine which layout to use
  const useMobileLayout = isMobile || showMobileCards;
  const useScrollLayout = isTablet && !showMobileCards;

  // Filter columns for mobile (remove hidden columns)
  const visibleColumns = useMobileLayout 
    ? columns.filter(col => !col.hideOnMobile)
    : columns;

  // Render loading state
  if (isLoading) {
    return (
      <div className={cn('relative', className)}>
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      </div>
    );
  }

  // Render empty state
  if (!data || data.length === 0) {
    return (
      <div className={cn('relative', className)}>
        <div className="flex items-center justify-center py-8 text-gray-500 dark:text-gray-400">
          {emptyMessage}
        </div>
      </div>
    );
  }

  // Mobile card layout
  if (useMobileLayout) {
    return (
      <div className={cn('space-y-3', className)}>
        {data.map((item, index) => {
          const key = keyExtractor(item, index);
          
          // Use custom mobile card renderer if provided
          if (renderMobileCard) {
            return (
              <div
                key={key}
                className={cn(
                  'bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4',
                  onRowClick && 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors'
                )}
                onClick={() => onRowClick?.(item, index)}
              >
                {renderMobileCard(item, index)}
              </div>
            );
          }

          // Default mobile card layout
          return (
            <div
              key={key}
              className={cn(
                'bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 space-y-2',
                onRowClick && 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors'
              )}
              onClick={() => onRowClick?.(item, index)}
            >
              {visibleColumns.map((column) => {
                const value = typeof column.accessor === 'function' 
                  ? column.accessor(item)
                  : item[column.accessor];
                
                const label = column.mobileLabel || column.header;
                
                return (
                  <div key={column.key} className="flex justify-between items-start">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-0 flex-1">
                      {label}:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white ml-2 text-right">
                      {value}
                    </span>
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    );
  }

  // Desktop/Tablet table layout
  return (
    <div className={cn('relative', className)}>
      <div className={cn(
        'overflow-x-auto',
        useScrollLayout && 'scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600'
      )}>
        <table className="w-full">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              {visibleColumns.map((column) => (
                <th
                  key={column.key}
                  className={cn(
                    'px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider',
                    column.headerClassName
                  )}
                  style={column.width ? { width: column.width } : undefined}
                >
                  {column.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {data.map((item, index) => {
              const key = keyExtractor(item, index);
              
              return (
                <tr
                  key={key}
                  className={cn(
                    'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors',
                    onRowClick && 'cursor-pointer'
                  )}
                  onClick={() => onRowClick?.(item, index)}
                >
                  {visibleColumns.map((column) => {
                    const value = typeof column.accessor === 'function' 
                      ? column.accessor(item)
                      : item[column.accessor];
                    
                    return (
                      <td
                        key={column.key}
                        className={cn(
                          'px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white',
                          column.className
                        )}
                      >
                        {value}
                      </td>
                    );
                  })}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default MobileTable;

// Helper component for mobile table actions
export const MobileTableActions: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <div className={cn('flex items-center justify-end space-x-2 mt-2', className)}>
      {children}
    </div>
  );
};

// Helper component for mobile table status badges
export const MobileTableBadge: React.FC<{
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error';
  className?: string;
}> = ({ children, variant = 'default', className }) => {
  const variantClasses = {
    default: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
    success: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  };

  return (
    <span className={cn(
      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
      variantClasses[variant],
      className
    )}>
      {children}
    </span>
  );
};

// Helper component for mobile table buttons
export const MobileTableButton: React.FC<{
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'icon';
  size?: 'small' | 'default';
  className?: string;
  disabled?: boolean;
}> = ({ 
  children, 
  onClick, 
  variant = 'secondary', 
  size = 'small',
  className,
  disabled = false 
}) => {
  const buttonClasses = touchButton(
    size === 'small' ? 'small' : variant,
    cn(
      'transition-colors',
      disabled && 'opacity-50 cursor-not-allowed',
      className
    )
  );

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={buttonClasses}
    >
      {children}
    </button>
  );
};
