/**
 * Mobile-Responsive Pagination Component
 * 
 * A pagination component that adapts to different screen sizes:
 * - Desktop: Full pagination with page numbers
 * - Tablet: Simplified pagination with prev/next and current page
 * - Mobile: Minimal pagination with just prev/next buttons
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { useIsMobile, useIsTablet, touchButton } from '@/utils/mobile-responsive';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';

export interface MobilePaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  isLoading?: boolean;
  showPageInfo?: boolean;
  className?: string;
  pageSize?: number;
  totalItems?: number;
}

const MobilePagination: React.FC<MobilePaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  isLoading = false,
  showPageInfo = true,
  className,
  pageSize,
  totalItems,
}) => {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();

  // Don't render if there's only one page or no pages
  if (totalPages <= 1) {
    return null;
  }

  const canGoPrevious = currentPage > 1;
  const canGoNext = currentPage < totalPages;

  // Calculate page info
  const startItem = pageSize && totalItems ? (currentPage - 1) * pageSize + 1 : null;
  const endItem = pageSize && totalItems 
    ? Math.min(currentPage * pageSize, totalItems) 
    : null;

  // Mobile layout - minimal pagination
  if (isMobile) {
    return (
      <div className={cn('flex items-center justify-between px-4 py-3', className)}>
        {/* Page info */}
        {showPageInfo && (
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {totalItems && pageSize ? (
              `${startItem}-${endItem} of ${totalItems}`
            ) : (
              `Page ${currentPage} of ${totalPages}`
            )}
          </div>
        )}

        {/* Navigation buttons */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={!canGoPrevious || isLoading}
            className={touchButton('secondary', cn(
              'flex items-center space-x-1',
              (!canGoPrevious || isLoading) && 'opacity-50 cursor-not-allowed'
            ))}
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="text-sm">Prev</span>
          </button>

          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={!canGoNext || isLoading}
            className={touchButton('secondary', cn(
              'flex items-center space-x-1',
              (!canGoNext || isLoading) && 'opacity-50 cursor-not-allowed'
            ))}
          >
            <span className="text-sm">Next</span>
            <ChevronRight className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  }

  // Tablet layout - simplified pagination
  if (isTablet) {
    return (
      <div className={cn('flex items-center justify-between px-6 py-4', className)}>
        {/* Page info */}
        {showPageInfo && (
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {totalItems && pageSize ? (
              `Showing ${startItem}-${endItem} of ${totalItems} results`
            ) : (
              `Page ${currentPage} of ${totalPages}`
            )}
          </div>
        )}

        {/* Navigation */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={!canGoPrevious || isLoading}
            className={touchButton('secondary', cn(
              'flex items-center space-x-1',
              (!canGoPrevious || isLoading) && 'opacity-50 cursor-not-allowed'
            ))}
          >
            <ChevronLeft className="h-4 w-4" />
            <span>Previous</span>
          </button>

          {/* Current page indicator */}
          <div className="flex items-center space-x-2 px-3">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {currentPage}
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              of {totalPages}
            </span>
          </div>

          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={!canGoNext || isLoading}
            className={touchButton('secondary', cn(
              'flex items-center space-x-1',
              (!canGoNext || isLoading) && 'opacity-50 cursor-not-allowed'
            ))}
          >
            <span>Next</span>
            <ChevronRight className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  }

  // Desktop layout - full pagination
  const getPageNumbers = () => {
    const pages: (number | 'ellipsis')[] = [];
    const maxVisiblePages = 7;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show first page
      pages.push(1);
      
      if (currentPage > 4) {
        pages.push('ellipsis');
      }
      
      // Show pages around current page
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      
      if (currentPage < totalPages - 3) {
        pages.push('ellipsis');
      }
      
      // Show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  return (
    <div className={cn('flex items-center justify-between px-6 py-4', className)}>
      {/* Page info */}
      {showPageInfo && (
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {totalItems && pageSize ? (
            `Showing ${startItem}-${endItem} of ${totalItems} results`
          ) : (
            `Page ${currentPage} of ${totalPages}`
          )}
        </div>
      )}

      {/* Navigation */}
      <div className="flex items-center space-x-1">
        {/* Previous button */}
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={!canGoPrevious || isLoading}
          className={cn(
            'flex items-center space-x-1 px-3 py-2 text-sm font-medium rounded-md transition-colors',
            canGoPrevious && !isLoading
              ? 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              : 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
          )}
        >
          <ChevronLeft className="h-4 w-4" />
          <span>Previous</span>
        </button>

        {/* Page numbers */}
        <div className="flex items-center space-x-1">
          {getPageNumbers().map((page, index) => {
            if (page === 'ellipsis') {
              return (
                <div key={`ellipsis-${index}`} className="px-3 py-2">
                  <MoreHorizontal className="h-4 w-4 text-gray-400" />
                </div>
              );
            }

            const isCurrentPage = page === currentPage;
            
            return (
              <button
                key={page}
                onClick={() => onPageChange(page)}
                disabled={isLoading}
                className={cn(
                  'px-3 py-2 text-sm font-medium rounded-md transition-colors min-w-[40px]',
                  isCurrentPage
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700',
                  isLoading && 'cursor-not-allowed opacity-50'
                )}
              >
                {page}
              </button>
            );
          })}
        </div>

        {/* Next button */}
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={!canGoNext || isLoading}
          className={cn(
            'flex items-center space-x-1 px-3 py-2 text-sm font-medium rounded-md transition-colors',
            canGoNext && !isLoading
              ? 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              : 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
          )}
        >
          <span>Next</span>
          <ChevronRight className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};

export default MobilePagination;
