/**
 * Mobile Responsive Design Utilities
 * 
 * This file contains utilities and constants for implementing consistent
 * mobile-responsive design across the Referral container components.
 */

import { useState, useEffect } from 'react';

// Tailwind CSS breakpoints (mobile-first approach)
export const BREAKPOINTS = {
  sm: 640,   // Small devices (landscape phones, 640px and up)
  md: 768,   // Medium devices (tablets, 768px and up)
  lg: 1024,  // Large devices (desktops, 1024px and up)
  xl: 1280,  // Extra large devices (large desktops, 1280px and up)
  '2xl': 1536 // 2X Extra large devices (larger desktops, 1536px and up)
} as const;

// Touch target standards (WCAG AA compliance)
export const TOUCH_TARGETS = {
  minimum: 44,     // Minimum touch target size in pixels
  comfortable: 48, // Comfortable touch target size
  large: 56       // Large touch target size for primary actions
} as const;

// Mobile-specific spacing scale
export const MOBILE_SPACING = {
  xs: 'px-2 sm:px-3',
  sm: 'px-3 sm:px-4',
  md: 'px-4 sm:px-6',
  lg: 'px-6 sm:px-8',
  xl: 'px-8 sm:px-12'
} as const;

// Responsive text sizes
export const RESPONSIVE_TEXT = {
  xs: 'text-xs sm:text-sm',
  sm: 'text-sm sm:text-base',
  base: 'text-base sm:text-lg',
  lg: 'text-lg sm:text-xl lg:text-2xl',
  xl: 'text-xl sm:text-2xl lg:text-3xl',
  '2xl': 'text-2xl sm:text-3xl lg:text-4xl'
} as const;

// Mobile table patterns
export const TABLE_PATTERNS = {
  scroll: 'overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600',
  stack: 'block sm:table',
  hideHeaders: 'hidden sm:table-header-group',
  stackCells: 'block sm:table-cell'
} as const;

// Mobile card layouts
export const CARD_LAYOUTS = {
  single: 'grid grid-cols-1 gap-4',
  responsive: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4',
  stack: 'space-y-4 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-4 lg:grid-cols-3'
} as const;

// Mobile button variants
export const MOBILE_BUTTONS = {
  primary: 'min-h-[44px] min-w-[44px] px-4 py-2 text-base sm:text-sm font-medium rounded-md',
  secondary: 'min-h-[44px] min-w-[44px] px-3 py-2 text-sm font-medium rounded-md',
  icon: 'min-h-[44px] min-w-[44px] p-2 rounded-md',
  small: 'min-h-[36px] min-w-[36px] px-2 py-1 text-sm rounded'
} as const;

// Mobile form utilities
export const MOBILE_FORMS = {
  group: 'space-y-2 sm:space-y-3',
  input: 'min-h-[44px] w-full px-3 py-2 text-base sm:text-sm rounded-md',
  label: 'text-sm font-medium mb-1 block',
  error: 'text-xs text-red-600 dark:text-red-400 mt-1'
} as const;

// Mobile modal utilities
export const MOBILE_MODALS = {
  container: 'w-[95vw] max-w-lg mx-auto',
  content: 'max-h-[85vh] overflow-y-auto',
  header: 'px-4 py-3 border-b',
  body: 'px-4 py-3',
  footer: 'px-4 py-3 border-t'
} as const;

/**
 * Custom hook for responsive design
 * @param query - Media query string
 * @returns boolean indicating if the query matches
 */
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const media = window.matchMedia(query);
      if (media.matches !== matches) {
        setMatches(media.matches);
      }
      
      const listener = () => setMatches(media.matches);
      media.addEventListener('change', listener);
      return () => media.removeEventListener('change', listener);
    }
    return () => {};
  }, [matches, query]);

  return matches;
};

/**
 * Hook to detect mobile devices
 */
export const useIsMobile = (): boolean => {
  return useMediaQuery(`(max-width: ${BREAKPOINTS.md - 1}px)`);
};

/**
 * Hook to detect tablet devices
 */
export const useIsTablet = (): boolean => {
  return useMediaQuery(`(min-width: ${BREAKPOINTS.md}px) and (max-width: ${BREAKPOINTS.lg - 1}px)`);
};

/**
 * Hook to detect desktop devices
 */
export const useIsDesktop = (): boolean => {
  return useMediaQuery(`(min-width: ${BREAKPOINTS.lg}px)`);
};

/**
 * Utility function to combine responsive classes
 * @param baseClasses - Base CSS classes
 * @param responsiveClasses - Object with breakpoint keys and class values
 * @returns Combined class string
 */
export const responsiveClasses = (
  baseClasses: string,
  responsiveClasses: Partial<Record<keyof typeof BREAKPOINTS, string>>
): string => {
  const classes = [baseClasses];
  
  Object.entries(responsiveClasses).forEach(([breakpoint, classNames]) => {
    if (classNames) {
      const prefix = breakpoint === 'sm' ? 'sm:' : `${breakpoint}:`;
      const prefixedClasses = classNames.split(' ').map(cls => `${prefix}${cls}`).join(' ');
      classes.push(prefixedClasses);
    }
  });
  
  return classes.join(' ');
};

/**
 * Utility to create touch-friendly button classes
 * @param variant - Button variant
 * @param size - Button size
 * @returns CSS classes for touch-friendly buttons
 */
export const touchButton = (
  variant: 'primary' | 'secondary' | 'icon' | 'small' = 'primary',
  additionalClasses: string = ''
): string => {
  const baseClasses = MOBILE_BUTTONS[variant];
  return `${baseClasses} ${additionalClasses}`.trim();
};

/**
 * Utility to create responsive table classes
 * @param pattern - Table pattern to use
 * @returns CSS classes for responsive tables
 */
export const responsiveTable = (pattern: keyof typeof TABLE_PATTERNS): string => {
  return TABLE_PATTERNS[pattern];
};

/**
 * Utility to create mobile-friendly spacing
 * @param size - Spacing size
 * @returns CSS classes for responsive spacing
 */
export const mobileSpacing = (size: keyof typeof MOBILE_SPACING): string => {
  return MOBILE_SPACING[size];
};

/**
 * Utility to create responsive text classes
 * @param size - Text size
 * @returns CSS classes for responsive text
 */
export const responsiveText = (size: keyof typeof RESPONSIVE_TEXT): string => {
  return RESPONSIVE_TEXT[size];
};

/**
 * Utility to create mobile card layout classes
 * @param layout - Card layout pattern
 * @returns CSS classes for responsive card layouts
 */
export const cardLayout = (layout: keyof typeof CARD_LAYOUTS): string => {
  return CARD_LAYOUTS[layout];
};

/**
 * Utility to create mobile form classes
 * @param element - Form element type
 * @returns CSS classes for mobile-friendly forms
 */
export const mobileForm = (element: keyof typeof MOBILE_FORMS): string => {
  return MOBILE_FORMS[element];
};

/**
 * Utility to create mobile modal classes
 * @param element - Modal element type
 * @returns CSS classes for mobile-friendly modals
 */
export const mobileModal = (element: keyof typeof MOBILE_MODALS): string => {
  return MOBILE_MODALS[element];
};

/**
 * Utility to determine if touch events should be used
 * @returns boolean indicating if touch events are supported
 */
export const isTouchDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

/**
 * Utility to get viewport dimensions
 * @returns Object with viewport width and height
 */
export const getViewportDimensions = () => {
  if (typeof window === 'undefined') {
    return { width: 0, height: 0 };
  }
  
  return {
    width: window.innerWidth,
    height: window.innerHeight
  };
};

/**
 * Utility to check if element is in viewport
 * @param element - DOM element to check
 * @returns boolean indicating if element is visible
 */
export const isElementInViewport = (element: Element): boolean => {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
};
